import { RiCloseLine } from "@remixicon/react";
import { CustomDropdown } from "components";
import { useGetSavedSections } from "features/Reports/api";
import { addReportBlockAtIndex } from "features/Reports/store";
import React, { useMemo } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import "./styles.scss";

interface InsertSectionModalProps {
  show: boolean;
  onClose: () => void;
  index: number;
}

const InsertSectionModal: React.FC<InsertSectionModalProps> = ({
  show,
  onClose,
  index,
}) => {
  const { data: { data: reportBlocks = [] } = {} } = useGetSavedSections();

  const savedBlocks = useMemo(() => {
    return reportBlocks.map((block: any, idx: number) => ({
      label: `${idx + 1} - ${block.title}`,
      value: JSON.stringify(block),
    }));
  }, [reportBlocks]);

  const handleClose = () => {
    onClose();
  };

  const handleSelectItem = (value: string) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id, ...parsedValue } = JSON.parse(value);
    addReportBlockAtIndex(parsedValue, index);
    handleClose();
  };

  const handleAddSection = () => {
    const newSection = {
      title: "New Section",
      content: {
        type: "html",
        content: "",
      },
      position: 0,
      is_locked: false,
    };
    addReportBlockAtIndex(newSection, index);
    handleClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      keyboard={false}
      centered
      className="insert-section-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={handleClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-stretch flex-column"
          style={{ gap: "20px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Insert Section
            </h1>
          </div>

          <div className="d-flex flex-column gap-3">
            <Button
              variant=""
              className="w-100 border-blue fw-bold py-2"
              onClick={handleAddSection}
            >
              Insert Blank Section
            </Button>
            {savedBlocks?.length > 0 && (
              <CustomDropdown
                title="Insert Saved Section"
                items={savedBlocks}
                className="insert-saved-section"
                onSelect={handleSelectItem}
                preserveTitle
              />
            )}
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default InsertSectionModal;
