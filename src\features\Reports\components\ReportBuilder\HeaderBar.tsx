import {
  RiBracesLine,
  RiCheckboxCircleLine,
  RiEye2Fill,
  RiHtml5Line,
  RiQuillPenFill,
  RiSave2Fill,
  RiSurveyLine,
} from "@remixicon/react";
import { setReportTitle } from "features/Reports/store";
import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import ActionButton from "../ActionButton";
import ReportPreview from "./ReportPreview";
import useSaveReport from "./hooks/useSaveReport";

interface HeaderBarProps {
  title?: string;
}

const HeaderBar = ({ title = "" }: HeaderBarProps) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  // TEMPORARY: Toggle for JSON format (will be removed later)
  const [useJsonFormat, setUseJsonFormat] = useState(false);
  const titleRef = useRef<HTMLInputElement>(null);
  const { handleSaveReport } = useSaveReport({ useJsonFormat });

  useEffect(() => {
    setLocalTitle(title);
    if (titleRef.current) {
      titleRef.current.value = title || "";
    }
  }, [title]);

  const handleEditTitle = () => {
    setIsEditingTitle(true);
    setTimeout(() => titleRef.current?.focus(), 0); // Focus input after re-render
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        setReportTitle(newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };

  const handleCheckCompliance = () => {
    console.log("Check compliance clicked");
  };

  const handleShowPreview = () => {
    setShowPreview(true);
  };

  // TEMPORARY: Toggle handler for JSON format (will be removed later)
  const handleToggleFormat = () => {
    setUseJsonFormat(!useJsonFormat);
    toast.success(`Switched to ${!useJsonFormat ? "JSON" : "HTML"} format`);
  };

  return (
    <>
      <div className="header d-flex gap-3">
        <div
          className={`header-title rounded w-100 position-relative ${isEditingTitle ? "border-1 border-brown bg-white" : ""}`}
        >
          <input
            type="text"
            className="report-title-input w-100 text-center fw-bold"
            placeholder="Enter report title..."
            readOnly={!isEditingTitle}
            ref={titleRef}
            defaultValue={localTitle}
            onKeyDown={handleTitleKeyDown}
            onBlur={handleTitleBlur}
          />
          {isEditingTitle ? (
            <button className="edit-title-btn">
              <RiCheckboxCircleLine color="#ad986f" />
            </button>
          ) : (
            <button className="edit-title-btn" onClick={handleEditTitle}>
              <RiQuillPenFill />
            </button>
          )}
        </div>
        <div className="header-actions d-flex gap-2">
          <ActionButton
            icon={RiSurveyLine}
            onClick={handleCheckCompliance}
            title="Check Compliance"
          />
          <ActionButton
            icon={RiEye2Fill}
            onClick={handleShowPreview}
            title="Preview Report"
          />
          {/* TEMPORARY: JSON format toggle button (will be removed later) */}
          <ActionButton
            icon={useJsonFormat ? RiBracesLine : RiHtml5Line}
            onClick={handleToggleFormat}
            title={`Currently using ${useJsonFormat ? "JSON" : "HTML"} format - Click to switch`}
            className={useJsonFormat ? "active" : ""}
          />
          <ActionButton
            icon={RiSave2Fill}
            onClick={handleSaveReport}
            title="Save Report"
          />
        </div>
      </div>

      {showPreview && (
        <ReportPreview
          show={showPreview}
          onClose={() => setShowPreview(false)}
        />
      )}
    </>
  );
};

export default HeaderBar;
