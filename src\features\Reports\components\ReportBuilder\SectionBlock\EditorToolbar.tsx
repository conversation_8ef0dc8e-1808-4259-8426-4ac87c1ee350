import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Ri<PERSON><PERSON>2<PERSON>ill,
  RiTable<PERSON>ill,
} from "@remixicon/react";
import { Editor } from "@tiptap/react";
import { useEffect, useState } from "react";
import { Button } from "react-bootstrap";
import ImageUploadButton from "./ImageUploadButton";
import toast from "react-hot-toast";
import { useSaveReportSection } from "features/Reports/api";

interface EditorToolbarProps {
  editor: Editor | null;
  sectionId: string | undefined;
}

const EditorToolbar = ({ editor, sectionId }: EditorToolbarProps) => {
  const [isConfirmingSave, setIsConfirmingSave] = useState(false);
  const { mutateAsync: saveReportSection } = useSaveReportSection();

  useEffect(() => {
    if (isConfirmingSave) {
      setTimeout(() => setIsConfirmingSave(false), 3000);
    }
  }, [isConfirmingSave]);

  if (!editor) {
    return null;
  }

  const handleInsertTable = () => {
    editor
      .chain()
      .focus()
      .insertTable({ rows: 3, cols: 3, withHeaderRow: true })
      .run();
  };

  const handleInsertGraph = () => {
    editor.chain().focus().insertGraphPlaceholder().run();
  };

  const handleSaveContent = () => {
    if (!sectionId) return;
    setIsConfirmingSave(true);
  };

  const onSave = async () => {
    try {
      const res: any = await saveReportSection({
        id: sectionId,
      });
      if (res?.success) {
        toast.success(res?.message);
      }
    } catch (err: any) {
      console.log(err);
    } finally {
      setIsConfirmingSave(false);
    }
  };

  return (
    <>
      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertTable}
        title="Insert table"
        disabled={editor.isActive("table")}
      >
        <RiTableFill size={18} />
      </Button>

      <Button
        variant="outline-secondary"
        size="sm"
        onClick={handleInsertGraph}
        title="Insert graph placeholder"
      >
        <RiBarChartFill size={18} />
      </Button>

      <ImageUploadButton editor={editor} text="Add Image" />

      {isConfirmingSave ? (
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={onSave}
          title="Save section content"
        >
          <RiCheckFill size={18} />
        </Button>
      ) : (
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={handleSaveContent}
          title={
            sectionId ? "Save section content" : "Please save report first"
          }
        >
          <RiSave2Fill size={18} />
        </Button>
      )}
    </>
  );
};

export default EditorToolbar;
