import { RiArticleLine } from "@remixicon/react";
import { useAddReport, useEditReport } from "features/Reports/api";
import useReportStore, {
  resetReportState,
} from "features/Reports/store/report";
import { useInvalidateQuery } from "hooks";
import toast from "react-hot-toast";
import { useParams } from "react-router-dom";
import { setConfirmModalConfig } from "stores";
// TEMPORARY: Imports for JSON conversion (will be removed later)
import Highlight from "@tiptap/extension-highlight";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { generateJSON } from "@tiptap/html";
import StarterKit from "@tiptap/starter-kit";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";

// TEMPORARY: Extensions for JSON conversion (will be removed later)
const extensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  GraphPlaceholder,
  ContentLock,
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
];

export default function useSaveReport({ useJsonFormat = false } = {}) {
  const [invalidateQueries] = useInvalidateQuery();
  const { id: reportId } = useParams();

  const { mutateAsync: addReport } = useAddReport();
  const { mutateAsync: editReport } = useEditReport();

  const reportinfo: any = useReportStore.getState().reportInfo;

  const saveReport = async () => {
    try {
      // TEMPORARY: Transform content based on format toggle (will be removed later)
      let payload = reportinfo;

      if (useJsonFormat) {
        // Convert HTML content to JSON format
        payload = {
          ...reportinfo,
          sections: reportinfo.sections.map((section: any) => {
            if (
              typeof section.content === "object" &&
              section.content !== null &&
              section.content.type === "html"
            ) {
              try {
                const jsonContent = generateJSON(
                  section.content.content,
                  extensions
                );
                return {
                  ...section,
                  content: jsonContent,
                };
              } catch (error) {
                console.error("Error converting HTML to JSON:", error);
                return section; // Keep original if conversion fails
              }
            }
            return section;
          }),
        };
      }

      const body = {
        sections: payload.sections,
        title: payload.title,
      };
      const response: any = reportId
        ? await editReport({ id: reportId, body })
        : await addReport(payload);
      if (response?.success) {
        if (!reportId) {
          resetReportState();
        }
        toast.success(response?.message);
        invalidateQueries(["reports-list"]);
      }
    } catch (err) {
      console.log(err);
    }
  };

  const handleSaveReport = () => {
    if (!reportinfo.title) {
      toast.error("Please enter report title");
      return;
    }
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: saveReport,
        content: {
          heading: `${reportId ? "Update" : "Save"} Report`,
          description: `Are you sure you want to ${reportId ? "update" : "save"} this report?`,
        },
        iconColor: "#ad986f",
        icon: RiArticleLine,
      },
    });
  };

  return { handleSaveReport };
}
